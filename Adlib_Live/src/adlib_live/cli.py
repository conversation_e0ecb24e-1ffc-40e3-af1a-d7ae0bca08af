#!/usr/bin/env python3
"""
cli.py – Entry-point for Adlib_Live.

Loads environment variables and calls the audio capture loop directly.
"""
import os
from dotenv import load_dotenv

# Load DEEPGRAM_API_KEY and other env vars from .env
load_dotenv()

from adlib_live.audio.audio_stream import main as run


def main():
    """
    Run the audio_stream entry point (synchronous). No asyncio wrapper here,
    as audio_stream.main() manages its own async loop and shutdown.
    """
    run()


if __name__ == "__main__":
    main()
