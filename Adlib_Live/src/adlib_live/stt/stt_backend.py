"""
stt_backend.py  –  Cloud-only Deepgram streaming STT via aiohttp

Implements the Transcriber protocol with a dedicated receiver task:
  • start()  → opens a WS connection and launches _recv_loop
  • feed()   → sends one 20 ms PCM frame
  • finish() → signals end, awaits receiver completion, and returns text
"""
from __future__ import annotations
import os
import json
import asyncio
import aiohttp
from typing import Protocol, Optional, List

# ───────── Public interface ───────────
class Transcriber(Protocol):
    async def start(self)  -> None: ...
    async def feed(self, pcm: bytes) -> None: ...
    async def finish(self) -> str: ...

# ───────── Deepgram client ────────────
_WS_URL = (
    "wss://api.deepgram.com/v1/listen"
    "?model=nova-3-general"
    "&encoding=linear16&sample_rate=16000"
    "&punctuate=true"
)

class DeepgramStream(Transcriber):
    def __init__(self, api_key: Optional[str] = None):
        self._key = api_key or os.getenv("DEEPGRAM_API_KEY")
        if not self._key:
            raise RuntimeError("Set DEEPGRAM_API_KEY in env or .env file")
        self._session: Optional[aiohttp.ClientSession] = None
        self._ws: Optional[aiohttp.ClientWebSocketResponse] = None
        self._final_parts: List[str] = []
        self._recv_task: Optional[asyncio.Task] = None

    async def start(self) -> None:
        """
        Open a new WS connection and start the background receiver.
        """
        self._session = aiohttp.ClientSession(
            headers={"Authorization": f"Token {self._key}"}
        )
        self._ws = await self._session.ws_connect(_WS_URL)
        self._final_parts.clear()
        # Launch receiver loop
        self._recv_task = asyncio.create_task(self._recv_loop())

    async def feed(self, pcm: bytes) -> None:
        """
        Send one 20-ms PCM frame; receiver task handles incoming messages.
        """
        await self._ws.send_bytes(pcm)

    async def finish(self) -> str:
        """
        Signal end-of-utterance, wait for receiver to collect finals,
        then close WS and session, and return transcript.
        """
        # Notify Deepgram we have finished sending
        await self._ws.send_json({"type": "CloseStream"})
        # Wait for the receiver to process the final message
        if self._recv_task:
            await self._recv_task
        await self._ws.close()
        await self._session.close()
        text = " ".join(self._final_parts).strip()
        return text

    async def _recv_loop(self) -> None:
        """
        Continuously receive messages until WS closes.
        Buffer any 'is_final' transcripts.
        """
        async for msg in self._ws:
            if msg.type == aiohttp.WSMsgType.TEXT:
                data = json.loads(msg.data)
                if data.get("is_final"):
                    self._final_parts.append(
                        data["channel"]["alternatives"][0]["transcript"]
                    )
            # ignore other message types (ping/pong/binary)

# Factory to choose the Transcriber

def get_transcriber() -> Transcriber:
    return DeepgramStream()
