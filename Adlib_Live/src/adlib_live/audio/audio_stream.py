#!/usr/bin/env python3
"""
audio_stream.py – Cloud-first Stage-2 loop for Adlib_Live.

Captures mic frames → streams to Deepgram via WebSocket → injects cues.
Manages cleanup of aiohttp sessions on shutdown.
"""

import os
import queue
import sounddevice as sd
import numpy as np
import asyncio
from dotenv import load_dotenv

# Load DEEPGRAM_API_KEY
load_dotenv()

from adlib_live.stt.stt_backend import get_transcriber
from adlib_live.prompt.stage1_context import Stage1ContextExtractor
from adlib_live.prompt.stage2_prompt import Stage2PromptInjector

# ───────────────── Configuration ─────────────────
SAMPLE_RATE   = 16_000         # Deepgram requires 16 kHz PCM
FRAME_SAMPLES = 320            # 20 ms frames: 16_000 * 0.02
DEVICE        = None           # default system mic (or specify index)

# Thread-safe queue for PCM bytes
pcm_queue = queue.Queue()

# Track the active transcriber for cleanup
global_transcriber = None

def audio_callback(indata, frames, time_info, status):
    """Sounddevice callback: enqueue raw PCM bytes."""
    if status:
        print(f"Audio status: {status}")
    # indata is a numpy array view; convert to bytes
    pcm_queue.put(bytes(indata))

async def process_loop(context_state, injector):
    """
    Loop: for each utterance, open a stream, feed frames until final, then cue.
    """
    global global_transcriber
    loop = asyncio.get_running_loop()
    while True:
        # 1) Open STT stream
        transcriber = get_transcriber()
        global_transcriber = transcriber
        await transcriber.start()

        # 2) Feed frames until final transcript available
        while not getattr(transcriber, '_final_parts', None):
            # blocking get from queue in executor
            pcm = await loop.run_in_executor(None, pcm_queue.get)
            await transcriber.feed(pcm)

        # 3) Finish and get transcript
        transcript = await transcriber.finish()
        global_transcriber = None
        if transcript:
            cue = injector.inject_prompt(context_state, transcript)
            print(f"\n👂 {transcript}\n💡 {cue['text']}\n")

        # continue to next utterance

def main():
    """
    Entry point: sets up audio capture and runs the async loop.
    Handles Ctrl-C and ensures cleanup.
    """
    # Prepare context and injector
    context_state = Stage1ContextExtractor().prepare_and_persist()
    injector = Stage2PromptInjector()

    print("Starting audio capture. Speak into your microphone and pause to see cues.")
    try:
        with sd.RawInputStream(
            samplerate=SAMPLE_RATE,
            blocksize=FRAME_SAMPLES,
            dtype="int16",
            channels=1,
            callback=audio_callback,
            device=DEVICE,
        ):
            # Run async loop until interrupted
            asyncio.run(process_loop(context_state, injector))
    except KeyboardInterrupt:
        print("\nInterrupted by user. Shutting down...")
    finally:
        # Cleanup any open Deepgram session
        try:
            tr = global_transcriber
            if tr:
                sess = getattr(tr, '_session', None)
                ws = getattr(tr, '_ws', None)
                if ws and not ws.closed:
                    # close websocket and session
                    loop = asyncio.new_event_loop()
                    loop.run_until_complete(ws.close())
                    if sess:
                        loop.run_until_complete(sess.close())
                    loop.close()
        except Exception:
            pass
        # Drain queue
        while not pcm_queue.empty():
            try:
                pcm_queue.get_nowait()
            except Exception:
                break
        print("Goodbye!")

if __name__ == "__main__":
    main()
