# ─── pyproject.toml ───
[build-system]
requires = ["setuptools>=61", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name            = "adlib_live"
version         = "0.0.1"
description     = "Cloud-first Stage-2 core for Adlib"
readme          = "README.md"
requires-python = ">=3.9"

# Everything below here is setuptools-specific
[tool.setuptools]
package-dir = {"" = "src"}          # <-- tells setuptools that code lives in src/

[tool.setuptools.packages.find]
where = ["src"]                     # <-- scan the src/ tree for packages